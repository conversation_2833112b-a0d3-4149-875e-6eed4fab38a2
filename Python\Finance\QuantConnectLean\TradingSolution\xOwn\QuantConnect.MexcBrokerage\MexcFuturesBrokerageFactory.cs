using System;
using System.Collections.Generic;
using QuantConnect.Brokerages;
using QuantConnect.Configuration;
using QuantConnect.Data;
using QuantConnect.Interfaces;
using QuantConnect.Packets;
using QuantConnect.Securities;
using QuantConnect.Util;

namespace QuantConnect.Brokerages.Mexc {
  public class MexcFuturesBrokerageFactory: BrokerageFactory {
    public override Dictionary<string, string> BrokerageData => new Dictionary<string, string> {
      { "mexc-api-key", Config.Get("mexc-api-key", "") },
      { "mexc-api-secret", Config.Get("mexc-api-secret", "") },
      { "mexc-websocket-url", Config.Get("mexc-websocket-url", "wss://contract.mexc.com/edge") },
    };

    public MexcFuturesBrokerageFactory()
      : base(typeof(MexcFuturesBrokerage)) {
    }

    public override IBrokerageModel GetBrokerageModel(IOrderProvider orderProvider) {
      return new MexcFuturesBrokerageModel(AccountType.Margin);
    }

    public override IBrokerage CreateBrokerage(LiveNodePacket job, IAlgorithm algorithm) {
      var aggregator = Composer.Instance.GetExportedValueByTypeName<IDataAggregator>(
        Config.Get("data-aggregator", "QuantConnect.Lean.Engine.DataFeeds.AggregationManager"), forceTypeNameOnExisting: false);

      var brokerage = new MexcFuturesBrokerage(algorithm, aggregator, job);

      Composer.Instance.AddPart<IDataQueueHandler>(brokerage);

      return brokerage;
    }

    public override void Dispose() {
    }
  }
}
