{"profiles": {"QuantConnect.Lean.Launcher": {"commandName": "Project", "commandLineArgs": "--config D:\\work\\xstarwalker168\\Python\\Finance\\QuantConnectLean\\trading-bot-config.json --parameters tradeMode:2,symbol:SUI,currency:USDT,leverage:5 --results-destination-folder \"D:/work/xstarwalker168/Python/Finance/QuantConnectLean/QC-Log-Dir\" --algorithm-language CSharp --environment live-mexc --algorithm-location cCryptoBot.dll --data-folder \"D:/work/xstarwalker168/Python/Finance/QuantConnectLean/Data\"\r\n"}}}