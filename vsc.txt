2ececdcdad13

hoxP_rta*001a

6aadf^11235D2

D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\Launcher\bin\Release
%APPDATA%\Cursor\User\workspaceStorage
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

https://futures.flytogarden.com/api/v1/private/order/create
https://futures.greentreeone.com/api/v1/private/order/cancel_all



if exist "%APPDATA%\Cursor\User\workspaceStorage" rd /s /q "%APPDATA%\Cursor\User\workspaceStorage


if exist "%APPDATA%\Cursor\User\workspaceStorage" rd /s /q "%APPDATA%\Cursor\User\workspaceStorage" && if exist "C:\Users\<USER>\AppData\Roaming\Cursor\User\History" rd /s /q "C:\Users\<USER>\AppData\Roaming\Cursor\User\History"
