using QuantConnect.Data;
using QuantConnect.Interfaces;
using QuantConnect.Securities;
using QuantConnect.Packets;

namespace QuantConnect.Brokerages.Mexc {
  [BrokerageFactory(typeof(MexcFuturesBrokerageFactory))]
  public class MexcFuturesBrokerage: MexcBrokerage {
    public MexcFuturesBrokerage()
      : base("mexc") {
    }

    public MexcFuturesBrokerage(IDataAggregator aggregator)
      : base("mexc", aggregator) {
    }

    public MexcFuturesBrokerage(IAlgorithm algorithm, IDataAggregator aggregator, LiveNodePacket job)
      : base("mexc", algorithm, aggregator, job) {
    }

    protected override SecurityType GetSupportedSecurityType() {
      return SecurityType.CryptoFuture;
    }
  }
}
